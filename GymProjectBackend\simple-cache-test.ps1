# Simple Cache Test Script
$baseUrl = "http://localhost:5165/api"

Write-Host "Cache Test Basladi" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# Test 1: GetMemberDetails - 4 defa cagir
Write-Host "TEST 1: GetMemberDetails" -ForegroundColor Yellow

for ($i = 1; $i -le 4; $i++) {
    Write-Host "Cagri $i/4:" -ForegroundColor Cyan
    $startTime = Get-Date
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/member/getmemberdetails" -Method GET
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        $dataCount = if ($response.Data) { $response.Data.Count } else { 0 }
        $source = if ($i -eq 1) { "DB" } else { "CACHE" }
        
        Write-Host "  Basarili - Sure: $([math]::Round($duration, 2))ms - Kayit: $dataCount - Kaynak: $source" -ForegroundColor Green
    }
    catch {
        Write-Host "  Hata: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 1000
}

Write-Host ""

# Test 2: GetAllPaginated - 4 defa cagir  
Write-Host "TEST 2: GetAllPaginated" -ForegroundColor Yellow

for ($i = 1; $i -le 4; $i++) {
    Write-Host "Cagri $i/4:" -ForegroundColor Cyan
    $startTime = Get-Date
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/member/getallpaginated?page=1&size=10" -Method GET
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        $dataCount = if ($response.Data -and $response.Data.Items) { $response.Data.Items.Count } else { 0 }
        $source = if ($i -eq 1) { "DB" } else { "CACHE" }
        
        Write-Host "  Basarili - Sure: $([math]::Round($duration, 2))ms - Kayit: $dataCount - Kaynak: $source" -ForegroundColor Green
    }
    catch {
        Write-Host "  Hata: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 1000
}

Write-Host ""

# Redis Cache Key Kontrolu
Write-Host "Redis Cache Key Kontrolu" -ForegroundColor Yellow
try {
    $redisKeys = docker exec gymkod-redis-dev redis-cli KEYS "gym:1:*"
    
    if ($redisKeys) {
        Write-Host "Cache Key'leri Bulundu:" -ForegroundColor Green
        foreach ($key in $redisKeys) {
            if ($key -and $key.Trim() -ne "") {
                Write-Host "  Key: $key" -ForegroundColor Cyan
            }
        }
    } else {
        Write-Host "Cache key bulunamadi" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Redis baglanti hatasi: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Cache Test Tamamlandi" -ForegroundColor Green
