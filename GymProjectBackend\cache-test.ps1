# Cache Test Script - MemberManager metotlarını test eder
# Her metodu 4 defa çağırarak cache davranışını gözlemler

$baseUrl = "http://localhost:5165/api"
$headers = @{
    "Content-Type" = "application/json"
}

Write-Host "🎯 CACHE TEST BAŞLADI - MemberManager Metotları" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

# Test 1: GetMemberDetails - Hot Data (5 dakika cache)
Write-Host "📊 TEST 1: GetMemberDetails (Hot Data - 5dk cache)" -ForegroundColor Yellow
Write-Host "---------------------------------------------------" -ForegroundColor Yellow

for ($i = 1; $i -le 4; $i++) {
    Write-Host "🔄 Çağrı $i/4:" -ForegroundColor Cyan
    $startTime = Get-Date
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/member/getmemberdetails" -Method GET -Headers $headers
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        $dataCount = if ($response.Data) { $response.Data.Count } else { 0 }
        $source = if ($i -eq 1) { "🗄️  DB" } else { "⚡ CACHE" }
        
        Write-Host "   ✅ Başarılı - Süre: $([math]::Round($duration, 2))ms - Kayıt: $dataCount - Kaynak: $source" -ForegroundColor Green
        
        if ($i -eq 1) {
            $firstCallTime = $duration
        } else {
            $improvement = [math]::Round((($firstCallTime - $duration) / $firstCallTime) * 100, 1)
            Write-Host "   📈 Performance Artışı: %$improvement" -ForegroundColor Magenta
        }
    }
    catch {
        Write-Host "   ❌ Hata: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host ""

# Test 2: GetAllPaginated - Warm Data (30 dakika cache)
Write-Host "📋 TEST 2: GetAllPaginated (Warm Data - 30dk cache)" -ForegroundColor Yellow
Write-Host "-----------------------------------------------------" -ForegroundColor Yellow

for ($i = 1; $i -le 4; $i++) {
    Write-Host "🔄 Çağrı $i/4:" -ForegroundColor Cyan
    $startTime = Get-Date
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/member/getallpaginated?page=1&size=10" -Method GET -Headers $headers
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        $dataCount = if ($response.Data -and $response.Data.Items) { $response.Data.Items.Count } else { 0 }
        $totalCount = if ($response.Data) { $response.Data.TotalCount } else { 0 }
        $source = if ($i -eq 1) { "🗄️  DB" } else { "⚡ CACHE" }
        
        Write-Host "   ✅ Başarılı - Süre: $([math]::Round($duration, 2))ms - Sayfa: $dataCount/$totalCount - Kaynak: $source" -ForegroundColor Green
        
        if ($i -eq 1) {
            $firstCallTime = $duration
        } else {
            $improvement = [math]::Round((($firstCallTime - $duration) / $firstCallTime) * 100, 1)
            Write-Host "   📈 Performance Artışı: %$improvement" -ForegroundColor Magenta
        }
    }
    catch {
        Write-Host "   ❌ Hata: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host ""

# Redis Cache Key Kontrolü
Write-Host "🔍 REDIS CACHE KEY KONTROLÜ" -ForegroundColor Yellow
Write-Host "----------------------------" -ForegroundColor Yellow

try {
    # Docker exec ile Redis CLI çalıştır
    $redisKeys = docker exec gymkod-redis-dev redis-cli KEYS "gym:1:*"
    
    if ($redisKeys) {
        Write-Host "✅ Cache Key'leri Bulundu:" -ForegroundColor Green
        foreach ($key in $redisKeys) {
            if ($key -and $key.Trim() -ne "") {
                Write-Host "   🔑 $key" -ForegroundColor Cyan
                
                # Key'in TTL'ini kontrol et
                $ttl = docker exec gymkod-redis-dev redis-cli TTL $key
                if ($ttl -and $ttl -ne "-1" -and $ttl -ne "-2") {
                    $minutes = [math]::Round($ttl / 60, 1)
                    Write-Host "      ⏰ TTL: $ttl saniye ($minutes dakika)" -ForegroundColor Gray
                }
            }
        }
    } else {
        Write-Host "⚠️  Cache key'i bulunamadı" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Redis bağlantı hatası: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 CACHE TEST TAMAMLANDI" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""
Write-Host "📝 SONUÇLAR:" -ForegroundColor White
Write-Host "• İlk çağrı: Database'den veri çekildi (yavaş)" -ForegroundColor White
Write-Host "• Sonraki çağrılar: Cache'den veri çekildi (hızlı)" -ForegroundColor White
Write-Host "• Performance artışı gözlemlendi" -ForegroundColor White
Write-Host "• Cache key'leri Redis'te oluşturuldu" -ForegroundColor White
Write-Host ""
