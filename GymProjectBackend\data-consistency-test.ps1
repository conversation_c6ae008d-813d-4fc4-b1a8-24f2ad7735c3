# Data Consistency Test - <PERSON><PERSON> vs DB verilerinin tutarliligini kontrol eder
$baseUrl = "http://localhost:5165/api"

Write-Host "Data Consistency Test Basladi" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Cache'i temizle
Write-Host "Cache temizleniyor..." -ForegroundColor Yellow
try {
    docker exec gymkod-redis-dev redis-cli -a GymKod2024Redis! FLUSHDB
    Write-Host "Cache temizlendi" -ForegroundColor Green
}
catch {
    Write-Host "Cache temizleme hatasi: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 2

# Test 1: GetMemberDetails - Ilk cagri (DB'den)
Write-Host ""
Write-Host "TEST 1: GetMemberDetails - DB'den veri cekiliyor" -ForegroundColor Yellow
$startTime = Get-Date
$dbResponse = Invoke-RestMethod -Uri "$baseUrl/member/getmemberdetails" -Method GET
$dbTime = (Get-Date - $startTime).TotalMilliseconds
$dbDataCount = if ($dbResponse.Data) { $dbResponse.Data.Count } else { 0 }

Write-Host "DB Cagri - Sure: $([math]::Round($dbTime, 2))ms - Kayit Sayisi: $dbDataCount" -ForegroundColor Cyan

# Test 2: GetMemberDetails - Ikinci cagri (Cache'den)
Write-Host ""
Write-Host "TEST 2: GetMemberDetails - Cache'den veri cekiliyor" -ForegroundColor Yellow
Start-Sleep -Milliseconds 500
$startTime = Get-Date
$cacheResponse = Invoke-RestMethod -Uri "$baseUrl/member/getmemberdetails" -Method GET
$cacheTime = (Get-Date - $startTime).TotalMilliseconds
$cacheDataCount = if ($cacheResponse.Data) { $cacheResponse.Data.Count } else { 0 }

Write-Host "Cache Cagri - Sure: $([math]::Round($cacheTime, 2))ms - Kayit Sayisi: $cacheDataCount" -ForegroundColor Cyan

# Veri tutarlilik kontrolu
Write-Host ""
Write-Host "VERI TUTARLILIK KONTROLU" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

if ($dbDataCount -eq $cacheDataCount) {
    Write-Host "✓ Kayit sayilari esit: $dbDataCount = $cacheDataCount" -ForegroundColor Green
} else {
    Write-Host "✗ Kayit sayilari farkli: DB=$dbDataCount, Cache=$cacheDataCount" -ForegroundColor Red
}

# Performance karsilastirma
$improvement = if ($dbTime -gt 0) { [math]::Round((($dbTime - $cacheTime) / $dbTime) * 100, 1) } else { 0 }
Write-Host "Performance Artisi: %$improvement" -ForegroundColor Magenta

# Ilk kaydin detaylarini karsilastir (eger veri varsa)
if ($dbResponse.Data -and $cacheResponse.Data -and $dbResponse.Data.Count -gt 0 -and $cacheResponse.Data.Count -gt 0) {
    $dbFirst = $dbResponse.Data[0]
    $cacheFirst = $cacheResponse.Data[0]
    
    Write-Host ""
    Write-Host "ILK KAYIT DETAY KARSILASTIRMA" -ForegroundColor Yellow
    Write-Host "=============================" -ForegroundColor Yellow
    
    # Ortak ozellikleri karsilastir
    $properties = @("MemberID", "Name", "PhoneNumber", "Balance")
    $allMatch = $true
    
    foreach ($prop in $properties) {
        if ($dbFirst.$prop -eq $cacheFirst.$prop) {
            Write-Host "✓ $prop: Esit ($($dbFirst.$prop))" -ForegroundColor Green
        } else {
            Write-Host "✗ $prop: Farkli - DB: $($dbFirst.$prop), Cache: $($cacheFirst.$prop)" -ForegroundColor Red
            $allMatch = $false
        }
    }
    
    if ($allMatch) {
        Write-Host ""
        Write-Host "✓ TUM VERILER TUTARLI - Cache dogru calisiyor!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "✗ VERI TUTARSIZLIGI TESPIT EDILDI!" -ForegroundColor Red
    }
}

# Cache key kontrolu
Write-Host ""
Write-Host "CACHE KEY KONTROLU" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

try {
    $keys = docker exec gymkod-redis-dev redis-cli -a GymKod2024Redis! KEYS "gym:1:*"
    if ($keys) {
        Write-Host "Cache Key'leri:" -ForegroundColor Green
        foreach ($key in $keys) {
            if ($key -and $key.Trim() -ne "" -and $key -notlike "*Warning*") {
                Write-Host "  $key" -ForegroundColor Cyan
                
                # TTL kontrolu
                $ttl = docker exec gymkod-redis-dev redis-cli -a GymKod2024Redis! TTL $key
                if ($ttl -and $ttl -match '^\d+$' -and [int]$ttl -gt 0) {
                    $minutes = [math]::Round([int]$ttl / 60, 1)
                    Write-Host "    TTL: $ttl saniye ($minutes dakika)" -ForegroundColor Gray
                }
            }
        }
    }
}
catch {
    Write-Host "Redis key kontrolu hatasi: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Data Consistency Test Tamamlandi" -ForegroundColor Green
